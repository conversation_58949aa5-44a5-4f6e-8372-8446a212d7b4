<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>FastAPI CORS 跨域测试</h1>
    
    <div class="test-section">
        <h2>API配置</h2>
        <p><strong>API地址:</strong> <span id="api-url">http://localhost:8000/api/v1</span></p>
        <button onclick="changeApiUrl()">修改API地址</button>
    </div>

    <div class="test-section">
        <h2>连接测试</h2>
        <button onclick="testConnection()">测试连接</button>
        <div id="connection-result"></div>
    </div>

    <div class="test-section">
        <h2>查询测试</h2>
        <input type="text" id="query-input" placeholder="输入测试问题" value="你好" style="width: 300px; padding: 5px;">
        <button onclick="testQuery()">发送查询</button>
        <div id="query-result"></div>
    </div>

    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <script>
        let API_BASE_URL = 'http://localhost:8000/api/v1';

        function changeApiUrl() {
            const newUrl = prompt('请输入新的API地址:', API_BASE_URL);
            if (newUrl) {
                API_BASE_URL = newUrl;
                document.getElementById('api-url').textContent = API_BASE_URL;
            }
        }

        async function testConnection() {
            const resultDiv = document.getElementById('connection-result');
            resultDiv.innerHTML = '<div class="loading">正在测试连接...</div>';

            try {
                const response = await axios.get(`${API_BASE_URL}/status`, {
                    timeout: 5000
                });
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ 连接成功!</h4>
                        <p><strong>状态码:</strong> ${response.status}</p>
                        <p><strong>响应数据:</strong></p>
                        <pre>${JSON.stringify(response.data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                console.error('连接测试失败:', error);
                
                let errorMessage = '未知错误';
                if (error.response) {
                    errorMessage = `服务器错误 (${error.response.status}): ${error.response.statusText}`;
                } else if (error.request) {
                    errorMessage = '无法连接到服务器，请检查：\n1. FastAPI服务是否启动\n2. 端口是否正确\n3. CORS配置是否正确';
                } else {
                    errorMessage = error.message;
                }
                
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 连接失败!</h4>
                        <p><strong>错误信息:</strong> ${errorMessage}</p>
                        <p><strong>完整错误:</strong></p>
                        <pre>${JSON.stringify(error.toJSON ? error.toJSON() : error.toString(), null, 2)}</pre>
                    </div>
                `;
            }
        }

        async function testQuery() {
            const resultDiv = document.getElementById('query-result');
            const query = document.getElementById('query-input').value.trim();
            
            if (!query) {
                alert('请输入查询内容');
                return;
            }

            resultDiv.innerHTML = '<div class="loading">正在发送查询...</div>';

            try {
                const response = await axios.post(`${API_BASE_URL}/query`, {
                    query: query,
                    max_results: 3,
                    similarity_threshold: 0.7
                }, {
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    timeout: 30000
                });
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ 查询成功!</h4>
                        <p><strong>问题:</strong> ${query}</p>
                        <p><strong>回答:</strong> ${response.data.answer}</p>
                        <p><strong>处理时间:</strong> ${response.data.processing_time?.toFixed(2)}秒</p>
                        <p><strong>源文档数量:</strong> ${response.data.total_sources}</p>
                        <p><strong>完整响应:</strong></p>
                        <pre>${JSON.stringify(response.data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                console.error('查询测试失败:', error);
                
                let errorMessage = '未知错误';
                if (error.response) {
                    errorMessage = `服务器错误 (${error.response.status}): ${error.response.data?.detail || error.response.statusText}`;
                } else if (error.request) {
                    errorMessage = '请求超时或无法连接到服务器';
                } else {
                    errorMessage = error.message;
                }
                
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 查询失败!</h4>
                        <p><strong>错误信息:</strong> ${errorMessage}</p>
                        <p><strong>完整错误:</strong></p>
                        <pre>${JSON.stringify(error.response?.data || error.toJSON?.() || error.toString(), null, 2)}</pre>
                    </div>
                `;
            }
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            testConnection();
        };
    </script>
</body>
</html>
