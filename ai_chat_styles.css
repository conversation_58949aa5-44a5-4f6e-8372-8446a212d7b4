/* Tailwind CSS 基础样式 */
*,
:after,
:before {
  border: 0 solid #e5e7eb;
  box-sizing: border-box;
}

:after,
:before {
  --tw-content: "";
}

:host,
html {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  font-family: Open Sans, ui-sans-serif, system-ui, sans-serif,
    Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  font-feature-settings: normal;
  font-variation-settings: normal;
  -moz-tab-size: 4;
  tab-size: 4;
  -webkit-tap-highlight-color: transparent;
}

body {
  line-height: inherit;
  margin: 0;
  padding: 24px 0;
  width: 100vw;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  min-width: 100vw;
  min-height: 100vh;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ffffff, #f8f8f8);
}

/* 基础工具类 */
.mb-3 {
  margin-bottom: 12px;
}
.mb-8 {
  margin-bottom: 32px;
}
.mr-2 {
  margin-right: 8px;
}
.mt-12 {
  margin-top: 48px;
}
.mt-2 {
  margin-top: 8px;
}
.mt-3 {
  margin-top: 12px;
}

.inline-block {
  display: inline-block;
}
.flex {
  display: flex;
}
.hidden {
  display: none;
}

.h-10 {
  height: 40px;
}
.h-16 {
  height: 64px;
}
.h-\[500px\] {
  height: 500px;
}
.min-h-screen {
  min-height: 100vh;
}

.w-10 {
  width: 40px;
}
.w-16 {
  width: 64px;
}
.w-\[1200px\] {
  width: 1200px;
}
.w-full {
  width: 100%;
}
.max-w-\[80\%\] {
  max-width: 80%;
}

.flex-1 {
  flex: 1 1 0%;
}
.flex-shrink-0 {
  flex-shrink: 0;
}

.cursor-pointer {
  cursor: pointer;
}

.flex-row {
  flex-direction: row;
}
.flex-col {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}

.items-start {
  align-items: flex-start;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}

.gap-2 {
  gap: 8px;
}
.gap-3 {
  gap: 12px;
}
.gap-4 {
  gap: 16px;
}
.gap-6 {
  gap: 24px;
}
.gap-8 {
  gap: 32px;
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-bottom: calc(8px * var(--tw-space-y-reverse));
  margin-top: calc(8px * (1 - var(--tw-space-y-reverse)));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-bottom: calc(12px * var(--tw-space-y-reverse));
  margin-top: calc(12px * (1 - var(--tw-space-y-reverse)));
}

.self-end {
  align-self: flex-end;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-y-auto {
  overflow-y: auto;
}

.rounded-2xl {
  border-radius: 48px;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: 24px;
}
.rounded-xl {
  border-radius: 36px;
}
.rounded-tl-none {
  border-top-left-radius: 0;
}
.rounded-tr-none {
  border-top-right-radius: 0;
}

.border {
  border-width: 1px;
}
.border-t {
  border-top-width: 1px;
}
.border-gray-300 {
  border-color: rgb(209 213 219);
}

/* 背景色 */
.bg-gray-100 {
  background-color: rgb(243 244 246);
}
.bg-gray-200 {
  background-color: rgb(229 231 235);
}
.bg-gray-300 {
  background-color: rgb(209 213 219);
}
.bg-white {
  background-color: rgb(255 255 255);
}

.bg-primary-100 {
  background-color: rgba(173, 30, 35, 0.1);
  color: rgba(0, 0, 0, 0.9) !important;
}

.bg-primary-500 {
  background-color: #ad1e23;
  color: hsla(0, 0%, 100%, 0.9) !important;
}

.bg-primary-600 {
  background-color: #99171c;
  color: hsla(0, 0%, 100%, 0.9) !important;
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.from-blue-50 {
  --tw-gradient-from: #eff6ff;
  --tw-gradient-to: rgba(239, 246, 255, 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-primary-50 {
  --tw-gradient-to: rgba(173, 30, 35, 0.05);
}

/* 内边距 */
.p-2 {
  padding: 8px;
}
.p-4 {
  padding: 16px;
}
.p-5 {
  padding: 20px;
}
.p-6 {
  padding: 24px;
}
.p-8 {
  padding: 32px;
}

.px-3 {
  padding-left: 12px;
  padding-right: 12px;
}
.px-4 {
  padding-left: 16px;
  padding-right: 16px;
}
.py-1 {
  padding-bottom: 4px;
  padding-top: 4px;
}
.py-2 {
  padding-bottom: 8px;
  padding-top: 8px;
}

/* 文本 */
.text-center {
  text-align: center;
}
.font-sans {
  font-family: Open Sans, ui-sans-serif, system-ui, sans-serif;
}

.text-2xl {
  font-size: 24px;
  line-height: 31.2px;
}
.text-3xl {
  font-size: 30px;
  line-height: 36px;
}
.text-sm {
  font-size: 14px;
  line-height: 21px;
}
.text-xl {
  font-size: 20px;
  line-height: 28px;
}

.font-bold {
  font-weight: 700;
}
.font-semibold {
  font-weight: 600;
}

.text-gray-600 {
  color: rgb(75 85 99);
}
.text-gray-700 {
  color: rgb(55 65 81);
}
.text-gray-500 {
  color: rgb(107 114 128);
}
.text-primary-500 {
  color: #ad1e23;
}
.text-primary-600 {
  color: #99171c;
}
.text-primary-700 {
  color: #891317;
}
.text-white {
  color: rgb(255 255 255);
}
.text-black {
  color: rgb(0 0 0);
}
.text-green-600 {
  color: rgb(22 163 74);
}
.text-red-600 {
  color: rgb(220 38 38);
}

/* 阴影 */
.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -4px rgba(0, 0, 0, 0.1);
}

.shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -2px rgba(0, 0, 0, 0.1);
}

.shadow-xl {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 8px 10px -6px rgba(0, 0, 0, 0.1);
}

/* 过渡效果 */
.transition-all {
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}
.transition-colors {
  transition: color, background-color,
    border-color 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}
.transition-shadow {
  transition: box-shadow 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}
.duration-300 {
  transition-duration: 0.3s;
}

/* 悬停效果 */
.hover\:-translate-y-1:hover {
  transform: translateY(-4px);
}

.hover\:bg-gray-300:hover {
  background-color: rgb(209 213 219);
}
.hover\:bg-primary-200:hover {
  background-color: rgba(173, 30, 35, 0.2);
}
.hover\:bg-primary-600:hover {
  background-color: #99171c;
}
.hover\:text-primary-600:hover {
  color: #99171c;
}
.hover\:text-primary-800:hover {
  color: #6e1014;
}
.hover\:shadow-lg:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -4px rgba(0, 0, 0, 0.1);
}
.hover\:shadow-xl:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 8px 10px -6px rgba(0, 0, 0, 0.1);
}

/* 焦点效果 */
.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:ring-2:focus {
  box-shadow: 0 0 0 2px rgba(173, 30, 35, 0.2);
}
.focus\:ring-primary-500:focus {
  --tw-ring-color: #ad1e23;
}

input:focus {
  border-color: #ad1e23;
  box-shadow: 0 0 0 2px rgba(173, 30, 35, 0.2);
}

/* 响应式 */
@media (min-width: 768px) {
  .md\:flex {
    display: flex;
  }
  .md\:hidden {
    display: none;
  }
}

@media (min-width: 1024px) {
  .lg\:w-1\/3 {
    width: 33.333333%;
  }
  .lg\:w-2\/3 {
    width: 66.666667%;
  }
  .lg\:flex-row {
    flex-direction: row;
  }
}

@media (max-width: 1200px) {
  #webcrumbs {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* 聊天相关样式 */
.chat-message {
  max-width: 80%;
  word-wrap: break-word;
  overflow-wrap: break-word;
  padding: 20px;
}

/* 参考来源样式 */
.source-list {
  margin-top: 12px;
  list-style: none;
  padding: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.source-item {
  position: relative;
  padding: 6px 10px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  height: 32px;
  overflow: visible;
  flex: 0 1 auto;
  min-width: 120px;
  max-width: fit-content;
  z-index: 1;
}

.source-item:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  z-index: 2;
}

.source-number {
  font-weight: 600;
  color: #ad1e23;
  margin-right: 6px;
  flex-shrink: 0;
  font-size: 13px;
}

.source-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #4b5563;
  font-size: 13px;
  max-width: 100px;
}

.source-title a {
  color: inherit;
  text-decoration: none;
  transition: color 0.2s ease;
}

.source-title a:hover {
  color: #ad1e23;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .source-list {
    gap: 6px;
  }
  .source-item {
    padding: 4px 8px;
    height: 28px;
    min-width: 100px;
  }
  .source-title {
    font-size: 12px;
    max-width: 180px;
  }
  .source-number {
    font-size: 12px;
  }
}

/* 桌面端聊天气泡框优化 */
@media (min-width: 1024px) {
  .chat-message {
    max-width: 60%;
    max-width: 600px;
  }
}

/* 移动端聊天气泡框优化 */
@media (max-width: 1023px) {
  .chat-message {
    max-width: 85%;
  }
}

/* 移动端菜单样式 */
#mobile-nav {
  width: 100%;
  background: white;
  border-top: 1px solid #e5e7eb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 16px;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

#mobile-nav ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

#mobile-nav li {
  border-bottom: 1px solid #f0f0f0;
}

#mobile-nav li:last-child {
  border-bottom: none;
}

#mobile-nav a {
  text-decoration: none;
  display: block;
  padding: 16px 20px;
  transition: all 0.2s ease;
  color: #374151;
  font-weight: 500;
}

#mobile-nav a:hover {
  color: #ad1e23 !important;
  background-color: #fef2f2;
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  #mobile-nav a {
    padding: 20px;
    font-size: 16px;
  }
}
