# CORS 跨域问题解决方案

## 问题描述

您遇到的错误信息：
```
AxiosError: Network Error
ERR_NETWORK
```

这是典型的CORS（跨域资源共享）问题。当从 `http://127.0.0.1:5500` 访问 `http://localhost:8000` 的API时，浏览器会阻止这种跨域请求。

## 解决方案

### ✅ 已完成的修复

我已经为您完成了以下修复：

1. **更新了CORS配置** (`backend/config/settings.py`)
   - 添加了Live Server常用端口 (5500, 5501)
   - 允许所有域名访问 (`*`)

2. **优化了中间件配置** (`backend/app/core/middleware.py`)
   - 设置更宽松的CORS策略
   - 允许所有HTTP方法和头部

3. **重启了FastAPI服务**
   - 新的CORS配置已生效
   - 服务运行正常

### 🔍 验证结果

通过测试确认：
- ✅ 服务状态: 正常 (254个文档，72.88MB存储)
- ✅ CORS配置: 正常 (允许跨域访问)
- ✅ OPTIONS预检请求: 成功

## 现在可以正常使用

### 1. 刷新测试页面

请刷新您的测试页面：`http://127.0.0.1:5500/test_cors.html`

现在应该能看到：
- ✅ 连接成功
- 正常的API响应数据

### 2. 测试ai_chat.html

您的 `ai_chat.html` 现在也应该能正常工作了：
- 连接状态显示为 "✅ 服务连接正常"
- 可以正常发送查询和接收回答

## 技术细节

### CORS配置说明

```python
# 当前的CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=False,  # 当allow_origins为*时必须为False
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["*"],
)
```

### 支持的访问方式

现在支持从以下地址访问API：
- `http://localhost:5500`
- `http://127.0.0.1:5500`
- `http://localhost:5501`
- `http://127.0.0.1:5501`
- `file://` (本地文件)
- 任何其他域名 (通配符 `*`)

## 故障排除

### 如果仍然有问题

1. **清除浏览器缓存**
   ```
   Ctrl + Shift + R (强制刷新)
   或清除浏览器缓存
   ```

2. **检查服务状态**
   ```bash
   curl http://localhost:8000/api/v1/status
   ```

3. **重新启动服务**
   ```bash
   python simple_restart.py
   ```

### 常见错误及解决方法

| 错误 | 原因 | 解决方法 |
|------|------|----------|
| Network Error | CORS配置问题 | ✅ 已修复 |
| Connection refused | 服务未启动 | 运行 `python start.py` |
| Timeout | 服务响应慢 | 检查数据库状态 |

## 安全注意事项

⚠️ **当前配置适用于开发环境**

在生产环境中，建议：
1. 移除通配符 `*` 配置
2. 只允许特定域名访问
3. 添加认证机制
4. 使用HTTPS

## 测试命令

### 测试CORS预检请求
```bash
curl -H "Origin: http://127.0.0.1:5500" \
     -H "Access-Control-Request-Method: GET" \
     -H "Access-Control-Request-Headers: Content-Type" \
     -X OPTIONS \
     http://localhost:8000/api/v1/status -v
```

### 测试实际API请求
```bash
curl -H "Origin: http://127.0.0.1:5500" \
     -H "Content-Type: application/json" \
     -X POST \
     -d '{"query":"你好","max_results":3,"similarity_threshold":0.7}' \
     http://localhost:8000/api/v1/query
```

## 下一步

现在您可以：
1. ✅ 正常使用 `test_cors.html` 测试API
2. ✅ 正常使用 `ai_chat.html` 进行对话
3. ✅ 从任何本地开发服务器访问API
4. ✅ 部署到其他域名（需要更新CORS配置）

如果还有任何问题，请检查浏览器开发者工具的控制台输出。
