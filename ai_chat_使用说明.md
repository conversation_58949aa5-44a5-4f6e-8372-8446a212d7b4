# ai_chat.html 跨域访问使用说明

## 概述

`ai_chat.html` 现在已经配置为可以跨域访问 FastAPI 项目的 API。这个文件可以在任何位置运行，不需要放在 FastAPI 项目目录内。

## 配置说明

### 1. FastAPI 服务端配置

已经更新了 CORS 配置以允许跨域访问：

```python
# backend/config/settings.py
allowed_origins: List[str] = [
    "http://localhost:3000",
    "http://127.0.0.1:3000", 
    "http://localhost:8000",
    "http://127.0.0.1:8000",
    "file://",  # 允许本地文件访问
    "*"  # 允许所有域名访问（开发环境）
]
```

### 2. 前端配置

在 `ai_chat.html` 中，API 地址配置在文件顶部：

```javascript
// API配置 - 可以根据需要修改这个地址
const API_BASE_URL = 'http://localhost:8000/api/v1';
```

## 使用步骤

### 1. 启动 FastAPI 服务

确保 FastAPI 服务正在运行：

```bash
cd C:\Users\<USER>\Desktop\fast-gzmdrw-chat
python start.py
```

或者手动启动：

```bash
cd backend
python -m uvicorn backend.app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 2. 打开 ai_chat.html

直接在浏览器中打开 `ai_chat.html` 文件：
- 双击文件
- 或者在浏览器中按 Ctrl+O 选择文件
- 或者将文件拖拽到浏览器窗口

### 3. 检查连接状态

页面加载后会自动检查 API 连接状态：
- ✅ 服务连接正常：表示可以正常使用
- ❌ 服务连接失败：需要检查 FastAPI 服务是否启动

## 功能特性

### 1. 智能对话
- 支持自然语言问答
- 基于 RAG 技术提供准确回答
- 显示参考来源和相似度

### 2. 参考来源
- 鼠标悬停显示详细内容
- 支持点击链接查看原文
- 移动端优化显示

### 3. 连接状态监控
- 实时显示 API 连接状态
- 自动检测服务可用性

### 4. 响应式设计
- 支持桌面和移动设备
- 自适应布局

## 故障排除

### 1. 连接失败

如果显示"服务连接失败"：

1. **检查 FastAPI 服务**
   ```bash
   # 测试服务是否运行
   curl http://localhost:8000/api/v1/status
   ```

2. **检查端口**
   - 确认 FastAPI 运行在 8000 端口
   - 如果使用其他端口，修改 `ai_chat.html` 中的 `API_BASE_URL`

3. **检查防火墙**
   - 确保 8000 端口没有被防火墙阻止

### 2. CORS 错误

如果浏览器控制台显示 CORS 错误：

1. **检查 CORS 配置**
   - 确认 `backend/config/settings.py` 中的 CORS 配置正确

2. **重启服务**
   - 修改配置后需要重启 FastAPI 服务

### 3. API 响应错误

如果查询返回错误：

1. **检查数据库**
   - 确认 ChromaDB 数据库有文档
   - 运行文档加载功能

2. **检查日志**
   - 查看 FastAPI 服务的控制台输出

## 测试工具

项目包含了一个测试工具 `test_cors.html`，可以用来：
- 测试 API 连接
- 验证 CORS 配置
- 调试查询功能

## 自定义配置

### 修改 API 地址

如果 FastAPI 服务运行在不同的地址或端口，修改 `ai_chat.html` 中的配置：

```javascript
const API_BASE_URL = 'http://your-server:port/api/v1';
```

### 修改查询参数

可以调整查询参数：

```javascript
const res = await axios.post(`${API_BASE_URL}/query`, {
  query: question,
  max_results: 5,        // 最大结果数量
  similarity_threshold: 0.7  // 相似度阈值
});
```

## 安全注意事项

当前配置允许所有域名访问（`"*"`），这适用于开发环境。在生产环境中，应该：

1. 移除 `"*"` 配置
2. 只允许特定域名访问
3. 考虑添加认证机制

## 技术栈

- **前端**: Vue.js 3, Axios, Tailwind CSS
- **后端**: FastAPI, ChromaDB, OpenAI API
- **跨域**: CORS 中间件

## 支持

如果遇到问题，请检查：
1. FastAPI 服务日志
2. 浏览器开发者工具控制台
3. 网络连接状态
