#!/usr/bin/env python3
"""
验证CORS跨域配置的脚本
"""
import requests
import json
import sys
from typing import Dict, Any

API_BASE_URL = "http://localhost:8000/api/v1"

def test_cors_headers(url: str, method: str = "GET", data: Dict[Any, Any] = None) -> bool:
    """测试CORS头部"""
    print(f"\n🔍 测试 {method} {url}")
    
    try:
        # 发送预检请求 (OPTIONS)
        if method == "POST":
            options_response = requests.options(
                url,
                headers={
                    'Origin': 'file://',
                    'Access-Control-Request-Method': 'POST',
                    'Access-Control-Request-Headers': 'Content-Type'
                }
            )
            print(f"   OPTIONS 预检请求状态: {options_response.status_code}")
            
            # 检查CORS头部
            cors_headers = {
                'Access-Control-Allow-Origin': options_response.headers.get('Access-Control-Allow-Origin'),
                'Access-Control-Allow-Methods': options_response.headers.get('Access-Control-Allow-Methods'),
                'Access-Control-Allow-Headers': options_response.headers.get('Access-Control-Allow-Headers'),
            }
            print(f"   CORS 头部: {json.dumps(cors_headers, indent=4, ensure_ascii=False)}")
        
        # 发送实际请求
        if method == "GET":
            response = requests.get(url, headers={'Origin': 'file://'})
        elif method == "POST":
            response = requests.post(
                url, 
                json=data,
                headers={
                    'Origin': 'file://',
                    'Content-Type': 'application/json'
                }
            )
        
        print(f"   实际请求状态: {response.status_code}")
        print(f"   响应CORS头部: {response.headers.get('Access-Control-Allow-Origin', '未设置')}")
        
        if response.status_code == 200:
            print("   ✅ 请求成功")
            return True
        else:
            print(f"   ❌ 请求失败: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ❌ 连接失败 - 请确保FastAPI服务正在运行")
        return False
    except Exception as e:
        print(f"   ❌ 错误: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    print("🚀 开始测试FastAPI CORS配置")
    print(f"📍 API地址: {API_BASE_URL}")
    
    # 测试状态端点
    status_success = test_cors_headers(f"{API_BASE_URL}/status", "GET")
    
    # 测试查询端点
    query_data = {
        "query": "你好",
        "max_results": 3,
        "similarity_threshold": 0.7
    }
    query_success = test_cors_headers(f"{API_BASE_URL}/query", "POST", query_data)
    
    return status_success and query_success

def check_service_status():
    """检查服务状态"""
    print("\n📊 检查服务状态")
    try:
        response = requests.get(f"{API_BASE_URL}/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 服务运行正常")
            print(f"   📄 文档数量: {data.get('documents_count', 'N/A')}")
            print(f"   💾 存储大小: {data.get('storage_size', 'N/A')}")
            return True
        else:
            print(f"   ❌ 服务状态异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 无法连接到服务: {e}")
        return False

def test_query_functionality():
    """测试查询功能"""
    print("\n💬 测试查询功能")
    try:
        response = requests.post(
            f"{API_BASE_URL}/query",
            json={
                "query": "你好",
                "max_results": 3,
                "similarity_threshold": 0.7
            },
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 查询成功")
            print(f"   📝 回答长度: {len(data.get('answer', ''))}")
            print(f"   📚 源文档数量: {data.get('total_sources', 0)}")
            print(f"   ⏱️ 处理时间: {data.get('processing_time', 0):.2f}秒")
            return True
        else:
            print(f"   ❌ 查询失败: {response.status_code}")
            print(f"   错误详情: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ 查询错误: {e}")
        return False

def generate_html_test():
    """生成HTML测试代码"""
    html_code = '''
<!-- 在ai_chat.html中使用的JavaScript代码示例 -->
<script>
const API_BASE_URL = 'http://localhost:8000/api/v1';

// 测试连接
async function testConnection() {
    try {
        const response = await fetch(`${API_BASE_URL}/status`);
        if (response.ok) {
            console.log('✅ API连接成功');
            return true;
        }
    } catch (error) {
        console.error('❌ API连接失败:', error);
        return false;
    }
}

// 发送查询
async function sendQuery(question) {
    try {
        const response = await fetch(`${API_BASE_URL}/query`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                query: question,
                max_results: 3,
                similarity_threshold: 0.7
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ 查询成功:', data);
            return data;
        }
    } catch (error) {
        console.error('❌ 查询失败:', error);
        return null;
    }
}
</script>
'''
    print("\n📋 HTML测试代码:")
    print(html_code)

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 FastAPI CORS 跨域配置验证工具")
    print("=" * 60)
    
    # 检查服务状态
    service_ok = check_service_status()
    if not service_ok:
        print("\n❌ 服务未运行，请先启动FastAPI服务")
        print("启动命令: python start.py")
        sys.exit(1)
    
    # 测试CORS配置
    cors_ok = test_api_endpoints()
    
    # 测试查询功能
    query_ok = test_query_functionality()
    
    # 生成测试代码
    generate_html_test()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print(f"   🔧 服务状态: {'✅ 正常' if service_ok else '❌ 异常'}")
    print(f"   🌐 CORS配置: {'✅ 正常' if cors_ok else '❌ 异常'}")
    print(f"   💬 查询功能: {'✅ 正常' if query_ok else '❌ 异常'}")
    
    if service_ok and cors_ok and query_ok:
        print("\n🎉 所有测试通过！ai_chat.html应该可以正常工作")
        print("📁 现在可以直接在浏览器中打开ai_chat.html文件")
    else:
        print("\n⚠️ 存在问题，请检查配置")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
