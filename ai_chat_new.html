<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>文文AI助手</title>
    <link rel="stylesheet" href="ai_chat_styles.css" />
    <!-- 引入字体和图标 -->
    <link
      href="https://fonts.googleapis.com/css2?family=Lato&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Open+Sans&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
      rel="stylesheet"
    />
  </head>
  <body>
    <div
      id="webcrumbs"
      class="w-full min-h-screen mx-auto bg-gradient-to-br from-blue-50 to-primary-50 p-8 font-sans flex flex-col items-center"
    >
      <header class="mb-8 w-full" style="max-width: 1600px; width: 100%">
        <!-- 主要header内容 -->
        <div class="flex justify-between items-center">
          <div class="flex items-center gap-4">
            <img
              src="images/gzmdrw_logo.png"
              alt="Logo"
              class="h-16 w-16 rounded-full shadow-md hover:shadow-lg transition-all duration-300"
              keywords="guiyang college, logo, education"
            />
            <a href="https://www.gzmdrw.cn/">
              <img
                src="images/rw_logo_red.png"
                class="h-16 transition-all duration-300"
              />
            </a>
          </div>
          <nav class="hidden md:flex items-center gap-6">
            <a
              href="https://www.gzmdrw.cn/home/"
              class="text-black hover:text-primary-600 hover:-translate-y-1 transition-all duration-300"
              >首页</a
            >
            <a
              href="https://www.gzmdrw.cn/tushuguan_subsite/jigoushezhi001/jiguanbumen001/"
              class="text-black hover:text-primary-600 hover:-translate-y-1 transition-all duration-300"
              >图书馆</a
            >
            <a
              href="https://www.gzmdrw.cn/xiaochangxinxiang/"
              class="text-black hover:text-primary-600 hover:-translate-y-1 transition-all duration-300"
              >校长书记信箱</a
            >
            <a
              href="https://www.720yun.com/vr/433jzgmuum5"
              class="text-black hover:text-primary-600 hover:-translate-y-1 transition-all duration-300"
              >3D全景校园</a
            >
            <a
              href="https://gzmyrw.jw.chaoxing.com/admin/login"
              class="text-black hover:text-primary-600 hover:-translate-y-1 transition-all duration-300"
              >教务系统</a
            >
            <a
              href="https://www.gzmdrw.cn/admin/login?redirect=%2Findex"
              class="text-black hover:text-primary-600 hover:-translate-y-1 transition-all duration-300"
              >管理员</a
            >
          </nav>
          <button
            id="mobile-menu-btn"
            class="md:hidden rounded-full p-2 bg-primary-100 hover:bg-primary-200 transition-colors"
            onclick="toggleMobileMenu()"
          >
            <span class="material-symbols-outlined">menu</span>
          </button>
        </div>

        <!-- 移动端菜单 - 显示在header底部 -->
        <div id="mobile-nav" class="hidden w-full md:hidden">
          <div class="mt-2 flex flex-wrap gap-2 p-4">
            <a
              href="https://www.gzmdrw.cn/home/"
              class="bg-primary-100 hover:bg-primary-200 rounded-full px-3 py-1 text-sm transition-colors text-black hover:text-primary-600"
              >首页</a
            >
            <a
              href="https://www.gzmdrw.cn/tushuguan_subsite/jigoushezhi001/jiguanbumen001/"
              class="bg-primary-100 hover:bg-primary-200 rounded-full px-3 py-1 text-sm transition-colors text-black hover:text-primary-600"
              >图书馆</a
            >
            <a
              href="https://www.gzmdrw.cn/xiaochangxinxiang/"
              class="bg-primary-100 hover:bg-primary-200 rounded-full px-3 py-1 text-sm transition-colors text-black hover:text-primary-600"
              >校长书记信箱</a
            >
            <a
              href="https://www.720yun.com/vr/433jzgmuum5"
              class="bg-primary-100 hover:bg-primary-200 rounded-full px-3 py-1 text-sm transition-colors text-black hover:text-primary-600"
              >3D全景校园</a
            >
            <a
              href="https://gzmyrw.jw.chaoxing.com/admin/login"
              class="bg-primary-100 hover:bg-primary-200 rounded-full px-3 py-1 text-sm transition-colors text-black hover:text-primary-600"
              >教务系统</a
            >
            <a
              href="https://www.gzmdrw.cn/admin/login?redirect=%2Findex"
              class="bg-primary-100 hover:bg-primary-200 rounded-full px-3 py-1 text-sm transition-colors text-black hover:text-primary-600"
              >管理员</a
            >
          </div>
        </div>
      </header>

      <main class="flex flex-col lg:flex-row gap-8">
        <section
          class="w-full lg:w-2/3 bg-white rounded-2xl shadow-xl overflow-hidden"
        >
          <div class="bg-primary-100 p-4">
            <h2 class="text-2xl font-bold text-center text-primary-700">
              文文AI 智能助手（我现在四岁，勉强学会说话，我会好好加油的！）
            </h2>
            <div class="text-center mt-2">
              <span id="connection-status" class="text-sm text-gray-500">
                正在检查连接...
              </span>
            </div>
          </div>

          <div
            id="chat-messages"
            class="h-[500px] p-6 overflow-y-auto flex flex-col gap-4"
          >
            <!-- 欢迎消息 -->
            <div class="flex items-start gap-3">
              <div
                class="w-10 h-10 rounded-full border-4 border-[#ad1e23] flex items-center justify-center flex-shrink-0"
              >
                <img
                  src="images/ww.png"
                  alt=""
                  class="w-full h-full rounded-full object-cover"
                />
              </div>
              <div
                class="bg-primary-100 rounded-2xl rounded-tl-none p-3 max-w-md break-words chat-message h-auto min-h-fit"
              >
                <p>
                  你好!我是贵阳人文科技学院的智能助手"文文"，致力于为师生提供便捷的信息服务。
                </p>
              </div>
            </div>

            <!-- 加载状态 -->
            <div
              id="loading-message"
              class="flex items-start gap-3"
              style="display: none"
            >
              <div
                class="w-10 h-10 rounded-full border-4 border-[#ad1e23] flex items-center justify-center flex-shrink-0"
              >
                <img
                  src="images/ww.png"
                  alt=""
                  class="w-full h-full rounded-full object-cover"
                />
              </div>
              <div class="bg-primary-100 rounded-2xl rounded-tl-none p-4">
                <p>正在思考中...</p>
              </div>
            </div>
          </div>

          <div class="border-t p-4">
            <div class="flex gap-2">
              <input
                id="user-input"
                type="text"
                placeholder="请输入你的问题..."
                class="flex-1 border border-gray-300 rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-all"
              />
              <button
                id="send-btn"
                class="w-10 h-10 rounded-full bg-primary-500 hover:bg-primary-600 transition-colors flex items-center justify-center"
              >
                <span class="material-symbols-outlined text-white">send</span>
              </button>
              <button
                id="clear-btn"
                class="w-10 h-10 rounded-full bg-gray-200 hover:bg-gray-300 transition-colors flex items-center justify-center"
              >
                <span class="material-symbols-outlined">refresh</span>
              </button>
            </div>
            <div class="mt-2 flex flex-wrap gap-2" id="quick-topics">
              <button
                class="topic-btn bg-primary-100 hover:bg-primary-200 rounded-full px-3 py-1 text-sm transition-colors"
                data-topic="专业设置"
              >
                专业设置
              </button>
              <button
                class="topic-btn bg-primary-100 hover:bg-primary-200 rounded-full px-3 py-1 text-sm transition-colors"
                data-topic="学费信息"
              >
                学费信息
              </button>
              <button
                class="topic-btn bg-primary-100 hover:bg-primary-200 rounded-full px-3 py-1 text-sm transition-colors"
                data-topic="奖学金政策"
              >
                奖学金政策
              </button>
              <button
                class="topic-btn bg-primary-100 hover:bg-primary-200 rounded-full px-3 py-1 text-sm transition-colors"
                data-topic="校园活动"
              >
                校园活动
              </button>
            </div>
          </div>
        </section>

        <aside class="w-full lg:w-1/3 flex flex-col gap-6">
          <article
            class="bg-white rounded-xl shadow-lg p-5 hover:shadow-xl transition-shadow"
          >
            <h3
              class="text-xl font-bold mb-3 text-primary-700 flex items-center"
            >
              <span class="material-symbols-outlined mr-2">school</span>学院简介
            </h3>
            <p class="text-gray-700">
              贵阳人文科技学院是一所集人文与科技为一体的综合性高等院校，致力于培养具有创新精神和实践能力的高素质人才。
            </p>
            <a
              href="https://www.gzmdrw.cn/"
              class="text-primary-600 hover:text-primary-800 transition-colors"
            >
              <p class="font-semibold mt-3">了解更多</p>
            </a>
          </article>

          <article
            class="bg-white rounded-xl shadow-lg p-5 hover:shadow-xl transition-shadow"
          >
            <h3
              class="text-xl font-bold mb-3 text-primary-700 flex items-center"
            >
              <span class="material-symbols-outlined mr-2">landscape</span
              >常用栏目
            </h3>
            <ul class="space-y-3">
              <li class="flex items-center gap-2">
                <a
                  href="https://www.gzmdrw.cn/news/"
                  class="text-primary-600 hover:text-primary-800 transition-colors"
                >
                  <p class="font-semibold">新闻动态</p>
                </a>
              </li>
              <li class="flex items-center gap-2">
                <a
                  href="https://www.gzmdrw.cn/notice/"
                  class="text-primary-600 hover:text-primary-800 transition-colors"
                >
                  <p class="font-semibold">通知公告</p>
                </a>
              </li>
              <li class="flex items-center gap-2">
                <a
                  href="https://www.gzmdrw.cn/academic/"
                  class="text-primary-600 hover:text-primary-800 transition-colors"
                >
                  <p class="font-semibold">学术交流</p>
                </a>
              </li>
            </ul>
          </article>

          <article
            class="bg-gradient-to-r from-primary-100 to-primary-50 rounded-xl shadow-lg p-5 text-primary-700 hover:shadow-xl transition-shadow"
          >
            <h3 class="text-xl font-bold mb-3 flex items-center">
              <span class="material-symbols-outlined mr-2">contact_support</span
              >学校信息
            </h3>
            <ul class="space-y-2">
              <li class="flex items-center gap-2">
                <span class="material-symbols-outlined">call</span>
                <p>0851-88308620</p>
              </li>
              <li class="flex items-center gap-2">
                <span class="material-symbols-outlined">mail</span>
                <p><EMAIL></p>
              </li>
              <li class="flex items-center gap-2">
                <span class="material-symbols-outlined">location_on</span>
                <p>
                  贵州省贵阳市花溪区花溪大学城<br />贵阳人文科技学院大学城校区
                </p>
              </li>
            </ul>
            <div class="mt-3 flex gap-3">
              <img src="images/enroll_code_qr.jpg" alt="招生二维码" />
            </div>
          </article>
        </aside>
      </main>

      <footer class="mt-12 text-center text-gray-600 text-sm">
        <p>© 2023 贵阳人文科技学院 - 版权所有</p>
        <div class="mt-2 flex justify-center gap-4">
          <a href="#" class="hover:text-primary-600 transition-colors"
            >隐私政策</a
          >
          <a href="#" class="hover:text-primary-600 transition-colors"
            >使用条款</a
          >
          <a href="#" class="hover:text-primary-600 transition-colors"
            >网站地图</a
          >
        </div>
      </footer>
    </div>

    <!-- 引入JavaScript库 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="ai_chat_script.js"></script>
  </body>
</html>
