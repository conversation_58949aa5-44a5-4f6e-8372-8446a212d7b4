<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试新版AI聊天</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>🧪 新版AI聊天测试</h1>
    
    <div class="test-section">
        <h2>📋 测试清单</h2>
        <div id="test-results">
            <div class="status warning">⏳ 正在检查文件...</div>
        </div>
        <button onclick="runTests()">🔄 重新测试</button>
    </div>

    <div class="test-section">
        <h2>🖥️ 聊天界面预览</h2>
        <p>如果下方显示正常，说明文件拆分成功：</p>
        <iframe src="ai_chat_new.html" title="AI聊天界面"></iframe>
    </div>

    <div class="test-section">
        <h2>🔧 快速修复</h2>
        <p>如果遇到问题，可以尝试以下操作：</p>
        <button onclick="checkAPI()">检查API连接</button>
        <button onclick="openOriginal()">打开原版对比</button>
        <button onclick="openCORS()">打开CORS测试</button>
    </div>

    <script>
        async function runTests() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="status warning">⏳ 正在测试...</div>';
            
            const tests = [
                { name: 'HTML文件', file: 'ai_chat_new.html' },
                { name: 'CSS文件', file: 'ai_chat_styles.css' },
                { name: 'JS文件', file: 'ai_chat_script.js' },
                { name: 'API连接', url: 'http://localhost:8000/api/v1/status' }
            ];
            
            let results = [];
            
            for (const test of tests) {
                try {
                    if (test.file) {
                        // 测试文件是否存在
                        const response = await fetch(test.file, { method: 'HEAD' });
                        if (response.ok) {
                            results.push(`<div class="status success">✅ ${test.name}: 文件存在</div>`);
                        } else {
                            results.push(`<div class="status error">❌ ${test.name}: 文件不存在 (${response.status})</div>`);
                        }
                    } else if (test.url) {
                        // 测试API连接
                        const response = await fetch(test.url);
                        if (response.ok) {
                            const data = await response.json();
                            results.push(`<div class="status success">✅ ${test.name}: 连接正常 (${data.documents_count}个文档)</div>`);
                        } else {
                            results.push(`<div class="status error">❌ ${test.name}: 连接失败 (${response.status})</div>`);
                        }
                    }
                } catch (error) {
                    results.push(`<div class="status error">❌ ${test.name}: ${error.message}</div>`);
                }
            }
            
            resultsDiv.innerHTML = results.join('');
        }
        
        async function checkAPI() {
            try {
                const response = await fetch('http://localhost:8000/api/v1/status');
                if (response.ok) {
                    const data = await response.json();
                    alert(`✅ API连接正常\n文档数量: ${data.documents_count}\n存储大小: ${data.storage_size}`);
                } else {
                    alert(`❌ API连接失败: ${response.status}`);
                }
            } catch (error) {
                alert(`❌ API连接错误: ${error.message}`);
            }
        }
        
        function openOriginal() {
            window.open('ai_chat.html', '_blank');
        }
        
        function openCORS() {
            window.open('test_cors.html', '_blank');
        }
        
        // 页面加载时自动运行测试
        window.onload = runTests;
    </script>
</body>
</html>
