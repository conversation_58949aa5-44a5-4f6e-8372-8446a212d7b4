"""
应用配置设置
"""
from typing import List
from pathlib import Path
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""

    # 应用基础配置
    app_name: str = "RAG Chat Application"
    app_version: str = "1.0.0"
    app_host: str = "0.0.0.0"
    app_port: int = 8000
    debug: bool = False
    environment: str = "production"

    # API配置
    api_v1_prefix: str = "/api/v1"

    # OpenAI配置
    openai_api_key: str
    openai_base_url: str = "https://api.openai.com/v1"
    openai_model: str = "gpt-4o-mini"
    embedding_model: str = "text-embedding-3-small"

    # 存储配置
    data_dir: Path = Path("./data")
    storage_dir: Path = Path("./storage")
    collection_name: str = "documents"

    # ChromaDB配置
    chroma_persist_directory: Path = Path("./storage")

    # CORS配置
    allowed_origins: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:8000",
        "http://127.0.0.1:8000",
        "file://",  # 允许本地文件访问
        "*"  # 允许所有域名访问（开发环境）
    ]

    # 数据库配置（为CMS模块准备）
    mysql_host: str = "localhost"
    mysql_port: int = 3306
    mysql_user: str = "root"
    mysql_password: str = "5Secsgo100"
    mysql_database: str = "chestnut_cms"
    
    # ChestnutCMS配置
    chestnut_cms_base_url: str = "https://www.gzmdrw.cn"

    class Config:
        env_file = [".env.local", ".env"]
        env_file_encoding = "utf-8"
        case_sensitive = False

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 确保目录存在
        self.data_dir.mkdir(exist_ok=True)
        self.storage_dir.mkdir(exist_ok=True)
        self.chroma_persist_directory.mkdir(exist_ok=True)


# 全局配置实例
settings = Settings()
