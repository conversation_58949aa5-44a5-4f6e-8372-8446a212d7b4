#!/usr/bin/env python3
"""
简单的FastAPI重启脚本
"""
import os
import sys
import time
import subprocess

def kill_port_8000():
    """终止占用8000端口的进程"""
    print("🔄 正在终止占用8000端口的进程...")
    
    try:
        if os.name == 'nt':  # Windows
            # 查找占用8000端口的进程
            result = subprocess.run(
                ['netstat', '-ano', '|', 'findstr', ':8000'],
                shell=True,
                capture_output=True,
                text=True
            )
            
            if result.stdout:
                lines = result.stdout.strip().split('\n')
                pids = set()
                for line in lines:
                    if 'LISTENING' in line:
                        parts = line.split()
                        if len(parts) >= 5:
                            pid = parts[-1]
                            pids.add(pid)
                
                for pid in pids:
                    try:
                        subprocess.run(['taskkill', '/F', '/PID', pid], 
                                     capture_output=True, check=True)
                        print(f"✅ 已终止进程 PID: {pid}")
                    except subprocess.CalledProcessError:
                        print(f"⚠️ 无法终止进程 PID: {pid}")
            else:
                print("ℹ️ 未找到占用8000端口的进程")
        else:  # Linux/Mac
            subprocess.run(['pkill', '-f', 'uvicorn.*main:app'], check=False)
            
        time.sleep(2)
        return True
    except Exception as e:
        print(f"❌ 终止进程时出错: {e}")
        return False

def start_fastapi():
    """启动FastAPI服务"""
    print("🚀 正在启动FastAPI服务...")
    
    # 切换到项目目录
    project_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(project_dir)
    
    try:
        cmd = [
            sys.executable, "-m", "uvicorn", 
            "backend.app.main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ]
        
        print(f"📝 执行命令: {' '.join(cmd)}")
        print("📁 工作目录:", os.getcwd())
        
        # 启动服务
        if os.name == 'nt':  # Windows
            # 在新的命令提示符窗口中启动
            subprocess.Popen(
                ['cmd', '/c', 'start', 'cmd', '/k'] + cmd,
                shell=True
            )
        else:  # Linux/Mac
            subprocess.Popen(cmd)
        
        print("✅ FastAPI服务启动命令已执行")
        return True
    except Exception as e:
        print(f"❌ 启动服务失败: {e}")
        return False

def check_service():
    """检查服务状态"""
    print("🔍 检查服务状态...")
    
    try:
        import requests
    except ImportError:
        print("⚠️ requests模块未安装，跳过服务检查")
        return True
    
    for i in range(15):  # 最多等待15秒
        try:
            response = requests.get("http://localhost:8000/api/v1/status", timeout=3)
            if response.status_code == 200:
                print("✅ 服务启动成功！")
                data = response.json()
                print(f"📊 文档数量: {data.get('documents_count', 'N/A')}")
                print(f"💾 存储大小: {data.get('storage_size', 'N/A')}")
                return True
        except Exception as e:
            pass
        
        print(f"⏳ 等待服务启动... ({i+1}/15)")
        time.sleep(1)
    
    print("❌ 服务启动超时，但可能仍在启动中")
    print("💡 请手动检查服务状态: http://localhost:8000/api/v1/status")
    return False

def test_cors():
    """测试CORS配置"""
    print("\n🌐 测试CORS配置...")
    
    try:
        import requests
        
        # 模拟跨域请求
        headers = {
            'Origin': 'http://127.0.0.1:5500',
            'Access-Control-Request-Method': 'GET',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        
        # 发送OPTIONS预检请求
        response = requests.options("http://localhost:8000/api/v1/status", headers=headers)
        print(f"OPTIONS预检请求状态: {response.status_code}")
        
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
        }
        
        print("CORS响应头:")
        for key, value in cors_headers.items():
            print(f"  {key}: {value}")
        
        if cors_headers['Access-Control-Allow-Origin'] == '*':
            print("✅ CORS配置正确，允许跨域访问")
            return True
        else:
            print("⚠️ CORS配置可能有问题")
            return False
            
    except Exception as e:
        print(f"❌ CORS测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔄 FastAPI服务重启工具 (简化版)")
    print("=" * 60)
    
    # 1. 终止现有进程
    kill_port_8000()
    
    # 2. 启动新服务
    if not start_fastapi():
        print("❌ 启动服务失败")
        return False
    
    # 3. 等待服务启动
    time.sleep(5)
    
    # 4. 检查服务状态
    service_ok = check_service()
    
    # 5. 测试CORS
    cors_ok = test_cors()
    
    print("\n" + "=" * 60)
    print("📋 重启结果:")
    print(f"   🔧 服务状态: {'✅ 正常' if service_ok else '❌ 异常'}")
    print(f"   🌐 CORS配置: {'✅ 正常' if cors_ok else '❌ 异常'}")
    
    if service_ok and cors_ok:
        print("\n🎉 重启成功！现在可以测试跨域访问了")
        print("🔗 测试链接: http://127.0.0.1:5500/test_cors.html")
    else:
        print("\n⚠️ 重启完成，但可能存在问题")
        print("💡 请手动检查服务状态")
    
    print("=" * 60)
    return service_ok

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()
